<template>
  <div class="std-dev-cross-backtest-page">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <span>标准差交叉策略回测</span>
        </div>
      </template>

      <!-- 参数输入表单 -->
      <el-form :model="form" label-width="120px" class="backtest-form">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="股票代码">
              <el-input v-model="form.stockCode" placeholder="例如: 000001.SZ"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开始日期">
              <el-date-picker v-model="form.startDate" type="date" placeholder="选择开始日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结束日期">
              <el-date-picker v-model="form.endDate" type="date" placeholder="选择结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD"></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="短周期">
              <el-input-number v-model="form.shortPeriod" :min="1" placeholder="例如: 2"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="长周期">
              <el-input-number v-model="form.longPeriod" :min="1" placeholder="例如: 40"></el-input-number>
            </el-form-item>
          </el-col>
           <el-col :span="8">
            <el-form-item label="初始资金">
              <el-input-number v-model="form.initialCapital" :min="1000" :step="10000" placeholder="例如: 100000"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="每次交易股数">
              <el-input-number v-model="form.sharesPerTrade" :min="100" :step="100" placeholder="例如: 100"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item>
              <el-button type="primary" @click="runBacktest" :loading="loading">执行回测</el-button>
              <el-button @click="resetForm">重置参数</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 回测结果展示 -->
    <div v-if="loading" class="loading-section">
      <el-skeleton :rows="10" animated />
    </div>

    <div v-if="error" class="error-section">
      <el-alert :title="error" type="error" show-icon :closable="false"></el-alert>
    </div>

    <div v-if="backtestResult && !loading && !error" class="results-section">
      <el-row :gutter="20">
        <!-- 左侧：核心指标 和 交易记录 -->
        <el-col :span="16">
          <el-card class="summary-card section-card">
            <template #header>核心指标</template>
            <el-descriptions :column="3" border>
              <el-descriptions-item label="股票代码">{{ backtestResult.stock_code }}</el-descriptions-item>
              <el-descriptions-item label="回测周期">{{ backtestResult.start_date }} 至 {{ backtestResult.end_date }}</el-descriptions-item>
              <el-descriptions-item label="初始资金">{{ formatCurrency(backtestResult.initial_capital) }}</el-descriptions-item>
              
              <el-descriptions-item label="最终价值">{{ formatCurrency(backtestResult.final_value) }}</el-descriptions-item>
              <el-descriptions-item label="总收益额">{{ formatCurrency(backtestResult.total_return) }}</el-descriptions-item>
              <el-descriptions-item label="总收益率">{{ formatPercentage(backtestResult.return_rate) }}</el-descriptions-item>

              <el-descriptions-item label="年化收益率">{{ formatPercentage(backtestResult.annualized_return) }}</el-descriptions-item>
              <el-descriptions-item label="最大回撤">{{ formatPercentage(backtestResult.max_drawdown) }}</el-descriptions-item>
              <el-descriptions-item label="胜率">{{ formatPercentage(backtestResult.win_rate) }}</el-descriptions-item>

              <el-descriptions-item label="夏普比率">{{ backtestResult.sharpe_ratio ? backtestResult.sharpe_ratio.toFixed(3) : 'N/A' }}</el-descriptions-item>
              <el-descriptions-item label="最大投入资金">{{ formatCurrency(backtestResult.max_investment) }}</el-descriptions-item>
              <el-descriptions-item label="投资效率">{{ formatPercentage(backtestResult.investment_efficiency) }}</el-descriptions-item>
              
              <el-descriptions-item label="交易总次数">{{ backtestResult.transaction_count }}</el-descriptions-item>
              <el-descriptions-item label="买入次数">{{ backtestResult.buy_count }}</el-descriptions-item>
              <el-descriptions-item label="卖出次数">{{ backtestResult.sell_count }}</el-descriptions-item>

              <el-descriptions-item label="最大连买次数">{{ backtestResult.max_consecutive_buys }}</el-descriptions-item>
              <el-descriptions-item label="最大连卖次数">{{ backtestResult.max_consecutive_sells }}</el-descriptions-item>
              <el-descriptions-item label="平均持仓天数">{{ backtestResult.avg_holding_period ? backtestResult.avg_holding_period.toFixed(2) : 'N/A' }} 天</el-descriptions-item>
            </el-descriptions>
          </el-card>

          <el-card class="transactions-card section-card">
            <template #header>交易记录</template>
            <el-table :data="backtestResult.transactions" stripe border max-height="500px">
              <el-table-column prop="date" label="日期" width="110" sortable></el-table-column>
              <el-table-column prop="type" label="类型" width="80">
                <template #default="scope">
                  <el-tag :type="scope.row.type === 'buy' ? 'success' : 'danger'">{{ scope.row.type === 'buy' ? '买入' : '卖出' }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="price" label="价格" width="100" :formatter="cellFormatCurrency"></el-table-column>
              <el-table-column prop="shares" label="股数" width="100"></el-table-column>
              <el-table-column label="成本/价值" width="120">
                 <template #default="scope">{{ formatCurrency(scope.row.type === 'buy' ? scope.row.cost : scope.row.value) }}</template>
              </el-table-column>
              <el-table-column prop="profit" label="利润" width="120" :formatter="cellFormatCurrencyNullable"></el-table-column>
              <el-table-column prop="cash_after" label="交易后现金" width="150" :formatter="cellFormatCurrency"></el-table-column>
              <el-table-column prop="position_after" label="交易后持仓" width="120"></el-table-column>
              <el-table-column prop="current_portfolio_value" label="交易后总资产" width="150" :formatter="cellFormatCurrency"></el-table-column>
              <el-table-column prop="total_investment" label="累计投入" width="150" :formatter="cellFormatCurrency"></el-table-column>
            </el-table>
          </el-card>
        </el-col>

        <!-- 右侧：投资组合价值图表 -->
        <el-col :span="8">
          <el-card class="chart-card section-card">
            <template #header>投资组合价值</template>
            <div ref="portfolioChart" style="width: 100%; height: 400px;"></div>
          </el-card>
          
          <el-card class="daily-portfolio-card section-card">
            <template #header>每日资产快照 (部分)</template>
            <el-table :data="paginatedDailyPortfolio" stripe border max-height="400px">
              <el-table-column prop="date" label="日期" sortable></el-table-column>
              <el-table-column prop="portfolio_value" label="总资产" :formatter="cellFormatCurrency"></el-table-column>
              <el-table-column prop="cash" label="现金" :formatter="cellFormatCurrency"></el-table-column>
              <el-table-column prop="stock_value" label="股票市值" :formatter="cellFormatCurrency"></el-table-column>
            </el-table>
            <el-pagination
              small
              background
              layout="prev, pager, next"
              :total="backtestResult.daily_portfolio.length"
              :page-size="dailyPortfolioPageSize"
              @current-change="handleDailyPortfolioPageChange"
              class="pagination-center"
            />
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from 'axios'; // 假设项目中已配置axios
import * as echarts from 'echarts'; // 引入ECharts

// API基础路径，请根据实际情况修改
const API_BASE_URL = 'http://localhost:8000/api/period_change_pct_stats';

// Helper function to format date as YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Get today's date and one year ago date
const today = new Date();
const oneYearAgo = new Date();
oneYearAgo.setFullYear(today.getFullYear() - 1);

const form = reactive({
  stockCode: '000001.SZ',
  startDate: formatDate(oneYearAgo), // 默认开始日期为一年前的今天
  endDate: formatDate(today),       // 默认结束日期为今天
  shortPeriod: 2,                 // 默认短周期为2
  longPeriod: 30,                  // 默认长周期为30
  initialCapital: 100000,
  sharesPerTrade: 100,
});

const loading = ref(false);
const error = ref(null);
const backtestResult = ref(null);
const portfolioChart = ref(null); // ECharts实例的DOM引用
let chartInstance = null; // ECharts实例

const resetForm = () => {
  form.stockCode = '000001.SZ';
  form.startDate = formatDate(oneYearAgo);
  form.endDate = formatDate(today);
  form.shortPeriod = 2;                 // 重置短周期为2
  form.longPeriod = 30;                  // 重置长周期为30
  form.initialCapital = 100000;
  form.sharesPerTrade = 100;
  backtestResult.value = null;
  error.value = null;
};

const runBacktest = async () => {
  if (!form.stockCode || !form.startDate || !form.endDate || !form.shortPeriod || !form.longPeriod || !form.initialCapital || !form.sharesPerTrade) {
    ElMessage.error('所有参数均为必填项！');
    return;
  }
  if (form.shortPeriod >= form.longPeriod) {
    ElMessage.error('短周期必须小于长周期！');
    return;
  }

  loading.value = true;
  error.value = null;
  backtestResult.value = null;

  try {
    const params = { ...form };
    // 后端期待的是下划线命名
    params.start_date = params.startDate;
    delete params.startDate;
    params.end_date = params.endDate;
    delete params.endDate;
    params.short_period = params.shortPeriod;
    delete params.shortPeriod;
    params.long_period = params.longPeriod;
    delete params.longPeriod;
    params.initial_capital = params.initialCapital;
    delete params.initialCapital;
    params.shares_per_trade = params.sharesPerTrade;
    delete params.sharesPerTrade;

    const response = await axios.get(`${API_BASE_URL}/backtest/analyze/${form.stockCode}`, { params });
    if (response.data) {
      backtestResult.value = response.data;
      ElMessage.success('回测成功！');
      await nextTick(); // 等待DOM更新
      if (backtestResult.value.portfolio_values && backtestResult.value.portfolio_values.length > 0) {
        initPortfolioChart();
      }
    } else {
      throw new Error('未收到有效数据');
    }
  } catch (err) {
    console.error("回测请求失败:", err);
    error.value = `回测失败: ${err.response?.data?.error || err.message || '未知错误'}`;
    ElMessage.error(error.value);
  } finally {
    loading.value = false;
  }
};

// 图表初始化
const initPortfolioChart = () => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  if (!portfolioChart.value || !backtestResult.value || !backtestResult.value.portfolio_values) return;

  chartInstance = echarts.init(portfolioChart.value);
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: params => {
        const date = params[0].axisValue;
        const value = params[0].value;
        const dailyData = backtestResult.value.daily_portfolio.find(d => d.date === date);
        let dailyInfo = '';
        if (dailyData) {
          dailyInfo = `日期: ${date}<br/>` +
                      `总资产: ${formatCurrency(value)}<br/>` +
                      `现金: ${formatCurrency(dailyData.cash)}<br/>` +
                      `股票市值: ${formatCurrency(dailyData.stock_value)}<br/>` +
                      `持仓股数: ${dailyData.position}<br/>` +
                      `当日股价: ${formatCurrency(dailyData.price)}`;
        } else {
          dailyInfo = `日期: ${date}<br/>总资产: ${formatCurrency(value)}`;
        }
        return dailyInfo;
      }
    },
    xAxis: {
      type: 'category',
      data: backtestResult.value.daily_portfolio.map(item => item.date), // 使用daily_portfolio的日期作为X轴
      boundaryGap: false,
    },
    yAxis: {
      type: 'value',
      scale: true,
      axisLabel: {
        formatter: function (value) {
          return formatCurrency(value, 0); // 格式化Y轴标签
        }
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 10,
        handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
        handleSize: '80%',
        handleStyle: {
          color: '#fff',
          shadowBlur: 3,
          shadowColor: 'rgba(0, 0, 0, 0.6)',
          shadowOffsetX: 2,
          shadowOffsetY: 2
        }
      }
    ],
    series: [
      {
        name: '投资组合价值',
        type: 'line',
        smooth: true,
        data: backtestResult.value.portfolio_values,
        itemStyle: {
            color: '#5470C6'
        },
        areaStyle: { // 添加面积图效果
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: 'rgba(84, 112, 198, 0.3)'
            }, {
                offset: 1,
                color: 'rgba(84, 112, 198, 0)'
            }])
        }
      }
    ],
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%', // 为dataZoom留出空间
      containLabel: true
    }
  };
  chartInstance.setOption(option);
};

// 格式化函数
const formatCurrency = (value, precision = 2) => {
  if (value === null || value === undefined) return 'N/A';
  return `¥${Number(value).toFixed(precision)}`;
};

const formatPercentage = (value, precision = 2) => {
  if (value === null || value === undefined) return 'N/A';
  return `${Number(value).toFixed(precision)}%`;
};

const cellFormatCurrency = (row, column, cellValue) => {
  return formatCurrency(cellValue);
};
const cellFormatCurrencyNullable = (row, column, cellValue) => {
  if (cellValue === null || cellValue === undefined) return '-';
  return formatCurrency(cellValue);
};

// 每日资产快照分页
const dailyPortfolioPage = ref(1);
const dailyPortfolioPageSize = ref(10);

const paginatedDailyPortfolio = computed(() => {
  if (!backtestResult.value || !backtestResult.value.daily_portfolio) {
    return [];
  }
  const start = (dailyPortfolioPage.value - 1) * dailyPortfolioPageSize.value;
  const end = start + dailyPortfolioPageSize.value;
  return backtestResult.value.daily_portfolio.slice(start, end);
});

const handleDailyPortfolioPageChange = (page) => {
  dailyPortfolioPage.value = page;
};

// 确保在组件卸载时销毁图表
import { onUnmounted } from 'vue';
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
});

</script>

<style scoped>
.std-dev-cross-backtest-page {
  padding: 20px;
}
.page-card {
  margin-bottom: 20px;
}
.backtest-form .el-date-picker {
  width: 100%;
}
.backtest-form .el-input-number {
  width: 100%;
}
.loading-section, .error-section {
  margin-top: 20px;
}
.results-section {
  margin-top: 20px;
}
.section-card {
  margin-bottom: 20px;
}
.card-header {
  font-size: 18px;
  font-weight: bold;
}
.el-descriptions {
  margin-top: 10px;
}
.pagination-center {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}
</style> 